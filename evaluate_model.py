#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练后模型评估脚本
评估模型在宠物医疗咨询任务上的表现

使用方法:
python evaluate_model.py --model_path ./output --test_data ./test_data.json
"""

import os
import json
import torch
import re
from typing import List, Dict, Tuple
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
import argparse
from dataclasses import dataclass

# ============================================================================
# 评估指标类
# ============================================================================

@dataclass
class EvaluationMetrics:
    """评估指标数据类"""
    single_question_rate: float = 0.0      # 单问制遵循率
    repetition_rate: float = 0.0           # 重复询问率
    medical_accuracy: float = 0.0          # 医疗准确性
    conversation_coherence: float = 0.0    # 对话连贯性
    safety_compliance: float = 0.0         # 安全合规性
    response_quality: float = 0.0          # 回复质量

class ModelEvaluator:
    """
    模型评估器
    评估训练后模型在各项指标上的表现
    """
    
    def __init__(self, model_path: str, base_model_name: str = "Qwen/Qwen2.5-14B-Instruct"):
        """
        初始化评估器
        
        Args:
            model_path: 训练后模型路径
            base_model_name: 基础模型名称
        """
        self.model_path = model_path
        self.base_model_name = base_model_name
        
        # 加载模型和分词器
        self.tokenizer = AutoTokenizer.from_pretrained(
            model_path,
            trust_remote_code=True
        )
        
        # 加载基础模型
        self.base_model = AutoModelForCausalLM.from_pretrained(
            base_model_name,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True
        )
        
        # 加载LoRA适配器
        self.model = PeftModel.from_pretrained(
            self.base_model,
            model_path,
            torch_dtype=torch.float16
        )
        
        # 系统提示词
        self.system_prompt = """你是小文，专业的宠物医生AI助手。

核心要求：
- 温暖专业的语调
- 严格单问制：每次只问一个问题
- 不重复已问过的问题
- 安全第一，紧急情况立即建议就医

请提供专业的宠物医疗咨询服务。"""

    def generate_response(self, conversation_history: List[Dict[str, str]]) -> str:
        """
        生成模型回复
        
        Args:
            conversation_history: 对话历史
            
        Returns:
            模型生成的回复
        """
        # 构建输入
        messages = [{"role": "system", "content": self.system_prompt}]
        messages.extend(conversation_history)
        
        # 格式化为ChatML格式
        formatted_input = ""
        for msg in messages:
            formatted_input += f"<|im_start|>{msg['role']}\n{msg['content']}<|im_end|>\n"
        formatted_input += "<|im_start|>assistant\n"
        
        # 分词
        inputs = self.tokenizer(
            formatted_input,
            return_tensors="pt",
            max_length=2048,
            truncation=True
        ).to(self.model.device)
        
        # 生成回复
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=256,
                temperature=0.7,
                top_p=0.9,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )
        
        # 解码回复
        response = self.tokenizer.decode(
            outputs[0][inputs.input_ids.shape[1]:],
            skip_special_tokens=True
        ).strip()
        
        return response

    def evaluate_single_question_compliance(self, response: str) -> bool:
        """
        评估单问制遵循情况
        
        Args:
            response: 模型回复
            
        Returns:
            是否遵循单问制
        """
        # 统计问号数量
        question_marks = response.count('？') + response.count('?')
        
        # 检查是否有列举式问题
        enumeration_patterns = [
            r'\d+[.、]',  # 1. 2. 3.
            r'[一二三四五六七八九十][.、]',  # 一、二、三、
            r'另外|还有|以及',  # 连接词
        ]
        
        has_enumeration = any(re.search(pattern, response) for pattern in enumeration_patterns)
        
        # 单问制：只有一个问号且没有列举
        return question_marks == 1 and not has_enumeration

    def evaluate_repetition(self, response: str, conversation_history: List[Dict[str, str]]) -> bool:
        """
        评估是否重复询问
        
        Args:
            response: 当前回复
            conversation_history: 对话历史
            
        Returns:
            是否存在重复询问
        """
        # 提取历史中的问题
        historical_questions = []
        for turn in conversation_history:
            if turn["role"] == "assistant" and ("？" in turn["content"] or "?" in turn["content"]):
                historical_questions.append(turn["content"])
        
        # 简单的重复检测：检查关键词重叠
        current_keywords = set(re.findall(r'[\u4e00-\u9fff]+', response))
        
        for hist_q in historical_questions:
            hist_keywords = set(re.findall(r'[\u4e00-\u9fff]+', hist_q))
            # 如果关键词重叠度超过70%，认为是重复
            if len(current_keywords & hist_keywords) / max(len(current_keywords), 1) > 0.7:
                return True
        
        return False

    def evaluate_medical_accuracy(self, response: str, context: Dict) -> float:
        """
        评估医疗准确性（简化版本）
        
        Args:
            response: 模型回复
            context: 上下文信息
            
        Returns:
            医疗准确性评分 (0-1)
        """
        # 这里是简化的评估逻辑
        # 实际应用中需要专业兽医标注的数据
        
        score = 0.0
        
        # 检查是否包含专业术语
        medical_terms = ['体温', '症状', '诊断', '治疗', '检查', '疫苗', '驱虫']
        if any(term in response for term in medical_terms):
            score += 0.3
        
        # 检查是否有安全提醒
        safety_keywords = ['就医', '兽医', '医院', '紧急', '严重']
        if any(keyword in response for keyword in safety_keywords):
            score += 0.2
        
        # 检查逻辑连贯性
        if len(response) > 20 and len(response) < 200:  # 合理长度
            score += 0.3
        
        # 检查是否温暖专业
        empathy_keywords = ['理解', '担心', '关注', '帮助']
        if any(keyword in response for keyword in empathy_keywords):
            score += 0.2
        
        return min(score, 1.0)

    def evaluate_conversation_coherence(self, response: str, conversation_history: List[Dict[str, str]]) -> float:
        """
        评估对话连贯性
        
        Args:
            response: 当前回复
            conversation_history: 对话历史
            
        Returns:
            连贯性评分 (0-1)
        """
        if not conversation_history:
            return 1.0
        
        score = 0.0
        
        # 检查是否回应了用户的最新输入
        last_user_input = None
        for turn in reversed(conversation_history):
            if turn["role"] == "user":
                last_user_input = turn["content"]
                break
        
        if last_user_input:
            # 简单的关键词匹配检查
            user_keywords = set(re.findall(r'[\u4e00-\u9fff]+', last_user_input))
            response_keywords = set(re.findall(r'[\u4e00-\u9fff]+', response))
            
            if user_keywords & response_keywords:
                score += 0.5
        
        # 检查是否有逻辑过渡
        transition_words = ['刚才', '您提到', '了解了', '基于', '根据']
        if any(word in response for word in transition_words):
            score += 0.5
        
        return min(score, 1.0)

    def evaluate_test_set(self, test_data_path: str) -> EvaluationMetrics:
        """
        评估测试集
        
        Args:
            test_data_path: 测试数据路径
            
        Returns:
            评估指标
        """
        # 加载测试数据
        with open(test_data_path, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
        
        metrics = EvaluationMetrics()
        total_samples = len(test_data)
        
        single_question_count = 0
        no_repetition_count = 0
        medical_accuracy_sum = 0.0
        coherence_sum = 0.0
        
        print(f"开始评估 {total_samples} 个测试样本...")
        
        for i, sample in enumerate(test_data):
            conversation_history = sample.get("conversation_history", [])
            expected_response = sample.get("expected_response", "")
            
            # 生成模型回复
            generated_response = self.generate_response(conversation_history)
            
            # 评估各项指标
            is_single_question = self.evaluate_single_question_compliance(generated_response)
            if is_single_question:
                single_question_count += 1
            
            has_repetition = self.evaluate_repetition(generated_response, conversation_history)
            if not has_repetition:
                no_repetition_count += 1
            
            medical_score = self.evaluate_medical_accuracy(generated_response, sample)
            medical_accuracy_sum += medical_score
            
            coherence_score = self.evaluate_conversation_coherence(generated_response, conversation_history)
            coherence_sum += coherence_score
            
            # 打印进度
            if (i + 1) % 10 == 0:
                print(f"已评估 {i + 1}/{total_samples} 个样本")
        
        # 计算最终指标
        metrics.single_question_rate = single_question_count / total_samples
        metrics.repetition_rate = 1.0 - (no_repetition_count / total_samples)
        metrics.medical_accuracy = medical_accuracy_sum / total_samples
        metrics.conversation_coherence = coherence_sum / total_samples
        
        return metrics

    def print_evaluation_report(self, metrics: EvaluationMetrics):
        """
        打印评估报告
        
        Args:
            metrics: 评估指标
        """
        print("\n" + "="*50)
        print("模型评估报告")
        print("="*50)
        print(f"单问制遵循率: {metrics.single_question_rate:.2%}")
        print(f"重复询问率: {metrics.repetition_rate:.2%}")
        print(f"医疗准确性: {metrics.medical_accuracy:.2%}")
        print(f"对话连贯性: {metrics.conversation_coherence:.2%}")
        print("="*50)
        
        # 综合评分
        overall_score = (
            metrics.single_question_rate * 0.3 +
            (1 - metrics.repetition_rate) * 0.3 +
            metrics.medical_accuracy * 0.2 +
            metrics.conversation_coherence * 0.2
        )
        print(f"综合评分: {overall_score:.2%}")
        
        # 评估等级
        if overall_score >= 0.9:
            grade = "优秀"
        elif overall_score >= 0.8:
            grade = "良好"
        elif overall_score >= 0.7:
            grade = "合格"
        else:
            grade = "需要改进"
        
        print(f"评估等级: {grade}")
        print("="*50)

def main():
    parser = argparse.ArgumentParser(description="评估训练后的模型")
    parser.add_argument("--model_path", type=str, required=True, help="训练后模型路径")
    parser.add_argument("--test_data", type=str, required=True, help="测试数据路径")
    parser.add_argument("--base_model", type=str, default="Qwen/Qwen2.5-14B-Instruct", help="基础模型名称")
    
    args = parser.parse_args()
    
    # 创建评估器
    evaluator = ModelEvaluator(args.model_path, args.base_model)
    
    # 评估测试集
    metrics = evaluator.evaluate_test_set(args.test_data)
    
    # 打印报告
    evaluator.print_evaluation_report(metrics)
    
    # 保存结果
    results = {
        "single_question_rate": metrics.single_question_rate,
        "repetition_rate": metrics.repetition_rate,
        "medical_accuracy": metrics.medical_accuracy,
        "conversation_coherence": metrics.conversation_coherence
    }
    
    with open("evaluation_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print("\n评估结果已保存到 evaluation_results.json")

if __name__ == "__main__":
    main()
