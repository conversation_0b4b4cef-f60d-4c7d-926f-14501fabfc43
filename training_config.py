# Qwen3-14B QLoRA 微调超参数配置
# 针对200条对话数据集（约2000个样本）的优化配置

import torch
from transformers import TrainingArguments
from peft import LoraConfig, TaskType

# ============================================================================
# 核心训练超参数配置
# ============================================================================

class QLoRATrainingConfig:
    """
    Qwen3-14B QLoRA微调配置类
    针对小规模高质量对话数据集优化
    """
    
    def __init__(self):
        # 基础模型配置
        self.model_name = "Qwen/Qwen2.5-14B-Instruct"  # 使用最新的Qwen2.5-14B
        self.max_seq_length = 2048  # 适合多轮对话的序列长度
        
        # LoRA配置 - 针对小数据集优化
        self.lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=32,  # 较小的rank，避免过拟合
            lora_alpha=64,  # alpha = 2 * r，平衡学习率
            lora_dropout=0.1,  # 适度dropout防止过拟合
            target_modules=[
                "q_proj", "k_proj", "v_proj", "o_proj",  # 注意力层
                "gate_proj", "up_proj", "down_proj"      # MLP层
            ],
            bias="none",  # 不训练bias参数
            use_rslora=True,  # 使用RSLoRA提升稳定性
        )
        
        # 训练超参数
        self.training_args = TrainingArguments(
            # 基础设置
            output_dir="./qwen3-14b-qlora-pet-medical",
            overwrite_output_dir=True,
            
            # 数据和批次设置
            per_device_train_batch_size=1,  # 小批次，适合14B模型
            per_device_eval_batch_size=1,
            gradient_accumulation_steps=16,  # 有效批次大小=16
            dataloader_num_workers=4,
            
            # 学习率和优化器
            learning_rate=2e-4,  # QLoRA推荐学习率
            lr_scheduler_type="cosine",  # 余弦退火
            warmup_ratio=0.1,  # 10%的步数用于预热
            weight_decay=0.01,  # 轻微权重衰减
            
            # 训练轮次和保存
            num_train_epochs=5,  # 小数据集适合多轮训练
            max_steps=-1,  # 使用epoch控制
            save_strategy="epoch",  # 每个epoch保存
            save_total_limit=3,  # 保留最近3个检查点
            
            # 评估设置
            evaluation_strategy="epoch",  # 每个epoch评估
            eval_steps=None,
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            
            # 日志和监控
            logging_strategy="steps",
            logging_steps=10,
            report_to="wandb",  # 使用wandb监控
            
            # 内存优化
            fp16=True,  # 使用半精度训练
            gradient_checkpointing=True,  # 梯度检查点节省内存
            dataloader_pin_memory=True,
            
            # 其他设置
            remove_unused_columns=False,
            seed=42,
            data_seed=42,
        )
        
        # 量化配置
        self.bnb_config = {
            "load_in_4bit": True,
            "bnb_4bit_quant_type": "nf4",
            "bnb_4bit_compute_dtype": torch.float16,
            "bnb_4bit_use_double_quant": True,
        }

# ============================================================================
# 针对不同数据规模的配置变体
# ============================================================================

class SmallDatasetConfig(QLoRATrainingConfig):
    """
    小数据集配置（<1000样本）
    更保守的参数设置，防止过拟合
    """
    def __init__(self):
        super().__init__()
        # 更小的LoRA rank
        self.lora_config.r = 16
        self.lora_config.lora_alpha = 32
        self.lora_config.lora_dropout = 0.15
        
        # 更保守的学习率
        self.training_args.learning_rate = 1e-4
        self.training_args.num_train_epochs = 3
        self.training_args.warmup_ratio = 0.05

class MediumDatasetConfig(QLoRATrainingConfig):
    """
    中等数据集配置（1000-5000样本）
    标准配置，适合您的2000样本数据集
    """
    def __init__(self):
        super().__init__()
        # 使用默认配置
        pass

class LargeDatasetConfig(QLoRATrainingConfig):
    """
    大数据集配置（>5000样本）
    更激进的参数设置
    """
    def __init__(self):
        super().__init__()
        # 更大的LoRA rank
        self.lora_config.r = 64
        self.lora_config.lora_alpha = 128
        self.lora_config.lora_dropout = 0.05
        
        # 更高的学习率
        self.training_args.learning_rate = 3e-4
        self.training_args.num_train_epochs = 3
        self.training_args.per_device_train_batch_size = 2

# ============================================================================
# 数据处理配置
# ============================================================================

class DataProcessingConfig:
    """
    数据处理相关配置
    """
    def __init__(self):
        # 对话格式配置
        self.conversation_format = "chatml"  # 使用ChatML格式
        self.max_length = 2048
        self.truncation_side = "left"  # 从左侧截断，保留最新对话
        
        # 数据增强配置
        self.use_data_augmentation = True
        self.augmentation_ratio = 0.2  # 20%的数据增强
        
        # 验证集配置
        self.train_test_split = 0.9  # 90%训练，10%验证
        self.random_seed = 42

# ============================================================================
# 推荐的训练策略
# ============================================================================

def get_recommended_config(dataset_size: int) -> QLoRATrainingConfig:
    """
    根据数据集大小返回推荐配置
    
    Args:
        dataset_size: 数据集样本数量
        
    Returns:
        推荐的训练配置
    """
    if dataset_size < 1000:
        print("检测到小数据集，使用保守配置防止过拟合")
        return SmallDatasetConfig()
    elif dataset_size < 5000:
        print("检测到中等数据集，使用标准配置")
        return MediumDatasetConfig()
    else:
        print("检测到大数据集，使用激进配置")
        return LargeDatasetConfig()

# ============================================================================
# 训练监控指标
# ============================================================================

MONITORING_METRICS = {
    "loss_metrics": [
        "train_loss",
        "eval_loss", 
        "perplexity"
    ],
    "custom_metrics": [
        "single_question_compliance_rate",  # 单问制遵循率
        "repetition_rate",                  # 重复询问率
        "medical_accuracy_score",           # 医疗准确性评分
        "conversation_coherence_score"      # 对话连贯性评分
    ],
    "early_stopping": {
        "patience": 3,
        "min_delta": 0.001,
        "monitor": "eval_loss"
    }
}

# ============================================================================
# 硬件要求估算
# ============================================================================

HARDWARE_REQUIREMENTS = {
    "minimum": {
        "gpu_memory": "16GB",
        "gpu_model": "RTX 4090 / V100",
        "system_ram": "32GB",
        "storage": "100GB SSD"
    },
    "recommended": {
        "gpu_memory": "24GB",
        "gpu_model": "RTX 4090 / A100",
        "system_ram": "64GB", 
        "storage": "200GB NVMe SSD"
    },
    "optimal": {
        "gpu_memory": "40GB+",
        "gpu_model": "A100 / H100",
        "system_ram": "128GB",
        "storage": "500GB NVMe SSD"
    }
}

# ============================================================================
# 使用示例
# ============================================================================

if __name__ == "__main__":
    # 根据您的数据集大小选择配置
    dataset_size = 2000  # 您的数据集大小
    config = get_recommended_config(dataset_size)

    print("推荐的训练配置:")
    print(f"LoRA rank: {config.lora_config.r}")
    print(f"学习率: {config.training_args.learning_rate}")
    print(f"训练轮次: {config.training_args.num_train_epochs}")
    print(f"批次大小: {config.training_args.per_device_train_batch_size}")
    print(f"梯度累积: {config.training_args.gradient_accumulation_steps}")

# ============================================================================
# 训练成本估算
# ============================================================================

def estimate_training_cost(config: QLoRATrainingConfig, dataset_size: int):
    """
    估算训练成本和时间

    Args:
        config: 训练配置
        dataset_size: 数据集大小
    """
    # 计算训练步数
    effective_batch_size = (config.training_args.per_device_train_batch_size *
                           config.training_args.gradient_accumulation_steps)
    steps_per_epoch = dataset_size // effective_batch_size
    total_steps = steps_per_epoch * config.training_args.num_train_epochs

    # 估算训练时间（基于RTX 4090）
    seconds_per_step = 3.5  # 经验值，14B模型在RTX 4090上
    total_hours = (total_steps * seconds_per_step) / 3600

    # 估算GPU成本（云服务器价格）
    gpu_cost_per_hour = 3.5  # USD，RTX 4090云服务器
    total_cost = total_hours * gpu_cost_per_hour

    print(f"\n训练成本估算:")
    print(f"总训练步数: {total_steps}")
    print(f"预计训练时间: {total_hours:.1f} 小时")
    print(f"预计GPU成本: ${total_cost:.2f} USD")
    print(f"推荐GPU: RTX 4090 (24GB) 或 A100 (40GB)")

    return {
        "total_steps": total_steps,
        "training_hours": total_hours,
        "estimated_cost": total_cost
    }
