# Qwen3-14B QLoRA微调超参数分析与建议

## 数据集特征分析

### 您的数据集特点
- **数据规模**: 200条对话 × 10轮 = 约2000个训练样本
- **数据质量**: 高质量对话数据，每轮都有明确的医疗咨询目标
- **任务复杂度**: 需要学习复杂的对话规则（单问制、避免重复等）
- **领域特异性**: 宠物医疗垂直领域，需要专业知识

### 数据规模评估
```
小数据集 (<1000样本): 需要非常保守的参数
中等数据集 (1000-5000样本): 您的情况，需要平衡的参数 ✓
大数据集 (>5000样本): 可以使用更激进的参数
```

## 核心超参数推荐与理由

### 1. LoRA配置参数

#### **推荐配置**
```python
r = 32                    # LoRA rank
lora_alpha = 64          # 缩放因子 (alpha = 2 * r)
lora_dropout = 0.1       # Dropout率
target_modules = [       # 目标模块
    "q_proj", "k_proj", "v_proj", "o_proj",  # 注意力层
    "gate_proj", "up_proj", "down_proj"      # MLP层
]
```

#### **参数选择理由**
- **r=32**: 适中的rank值，平衡表达能力和过拟合风险
  - 太小(r<16): 可能表达能力不足，难以学习复杂规则
  - 太大(r>64): 容易过拟合，特别是在小数据集上
- **lora_alpha=64**: 遵循alpha=2*r的经验法则，提供适当的学习率缩放
- **dropout=0.1**: 适度的正则化，防止过拟合但不影响学习能力

### 2. 学习率配置

#### **推荐配置**
```python
learning_rate = 2e-4     # 基础学习率
lr_scheduler_type = "cosine"  # 余弦退火
warmup_ratio = 0.1       # 10%预热
```

#### **选择理由**
- **2e-4**: QLoRA的经典学习率，比全量微调高一个数量级
  - 比1e-4更快收敛，比5e-4更稳定
- **余弦退火**: 在训练后期逐渐降低学习率，有助于收敛到更好的局部最优
- **10%预热**: 小数据集不需要太长预热，避免浪费训练步数

### 3. 批次大小配置

#### **推荐配置**
```python
per_device_train_batch_size = 1    # 单GPU批次大小
gradient_accumulation_steps = 16   # 梯度累积步数
# 有效批次大小 = 1 × 16 = 16
```

#### **选择理由**
- **小批次**: 14B模型内存占用大，batch_size=1避免OOM
- **梯度累积**: 通过累积16步达到有效批次大小16，平衡内存和训练稳定性
- **有效批次16**: 适合小数据集，不会导致梯度估计过于粗糙

### 4. 训练轮次配置

#### **推荐配置**
```python
num_train_epochs = 5     # 训练轮次
```

#### **选择理由**
- **5个epoch**: 小数据集需要多轮训练才能充分学习
  - 少于3轮: 可能学习不充分
  - 多于8轮: 容易过拟合
- **早停机制**: 配合验证集监控，防止过拟合

## 针对您数据集的特殊考虑

### 1. 对话长度处理
```python
max_seq_length = 2048    # 序列长度
truncation_side = "left" # 左侧截断
```
- **2048长度**: 足够容纳10轮对话，避免信息丢失
- **左侧截断**: 保留最新的对话内容，符合医疗咨询的时序特点

### 2. 数据增强策略
```python
# 建议的数据增强方法
augmentation_methods = [
    "paraphrase_questions",      # 改写问题表达
    "symptom_synonym_replacement", # 症状同义词替换
    "conversation_reordering",   # 对话顺序调整
    "context_expansion"          # 上下文扩展
]
```

### 3. 验证集划分
```python
train_test_split = 0.9   # 90%训练，10%验证
stratified_split = True  # 按对话类型分层
```

## 训练监控与调优策略

### 1. 关键监控指标
```python
primary_metrics = {
    "loss": "eval_loss",                    # 主要优化目标
    "perplexity": "eval_perplexity",       # 语言模型质量
    "custom": "single_question_rate"        # 业务指标
}
```

### 2. 早停策略
```python
early_stopping = {
    "patience": 3,           # 3个epoch无改善则停止
    "min_delta": 0.001,      # 最小改善阈值
    "monitor": "eval_loss"   # 监控指标
}
```

### 3. 学习率调优
如果训练过程中发现问题，可以尝试以下调整：
- **收敛太慢**: 增加学习率到3e-4
- **训练不稳定**: 降低学习率到1e-4
- **过拟合严重**: 增加dropout到0.15，减少rank到16

## 硬件要求与成本估算

### 1. 最低硬件要求
```
GPU: RTX 4090 (24GB) 或 V100 (32GB)
内存: 32GB DDR4
存储: 100GB SSD
```

### 2. 推荐硬件配置
```
GPU: A100 (40GB) 或 RTX 4090 (24GB)
内存: 64GB DDR4
存储: 200GB NVMe SSD
```

### 3. 训练成本估算
```
数据集大小: 2000样本
有效批次大小: 16
每轮步数: 125步
总训练步数: 625步 (5 epochs)
预计训练时间: 6-8小时 (RTX 4090)
云服务器成本: $21-28 USD
```

## 实际训练建议

### 1. 分阶段训练策略
```python
# 阶段1: 保守训练 (前3个epoch)
stage1_config = {
    "learning_rate": 1e-4,
    "lora_dropout": 0.15,
    "num_epochs": 3
}

# 阶段2: 精调训练 (后2个epoch)
stage2_config = {
    "learning_rate": 5e-5,
    "lora_dropout": 0.1,
    "num_epochs": 2
}
```

### 2. 超参数搜索空间
如果有计算资源，可以尝试网格搜索：
```python
search_space = {
    "learning_rate": [1e-4, 2e-4, 3e-4],
    "lora_r": [16, 32, 64],
    "lora_dropout": [0.05, 0.1, 0.15],
    "num_epochs": [3, 5, 8]
}
```

### 3. 训练技巧
- **梯度裁剪**: 使用max_grad_norm=1.0防止梯度爆炸
- **权重衰减**: weight_decay=0.01提供轻微正则化
- **混合精度**: 使用fp16节省内存和加速训练
- **梯度检查点**: gradient_checkpointing=True节省内存

## 预期效果与评估

### 1. 训练收敛指标
- **训练损失**: 应该从~2.5降到~1.5以下
- **验证损失**: 应该跟随训练损失下降，不应该上升
- **困惑度**: 应该从~12降到~4以下

### 2. 业务指标目标
- **单问制遵循率**: 从60%提升到85%+
- **重复询问率**: 从40%降低到10%以下
- **医疗逻辑准确性**: 从70%提升到85%+

### 3. 潜在问题与解决方案
- **过拟合**: 减少rank，增加dropout，使用更多数据增强
- **欠拟合**: 增加rank，提高学习率，增加训练轮次
- **训练不稳定**: 降低学习率，增加预热步数，检查数据质量

这个配置专门针对您的2000样本宠物医疗对话数据集优化，应该能够在避免过拟合的同时充分学习复杂的对话规则。
