#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen3-14B QLoRA微调训练脚本
针对宠物医疗对话数据集优化

使用方法:
python train_qwen_qlora.py --data_path ./data/pet_medical_conversations.json
"""

import os
import json
import torch
import wandb
from dataclasses import dataclass
from typing import Dict, List, Optional
from transformers import (
    AutoTokenizer, AutoModelForCausalLM,
    TrainingArguments, Trainer,
    BitsAndBytesConfig
)
from peft import LoraConfig, get_peft_model, TaskType
from datasets import Dataset
import argparse

# 导入配置
from training_config import MediumDatasetConfig, estimate_training_cost

# ============================================================================
# 数据处理类
# ============================================================================

class PetMedicalDataProcessor:
    """
    宠物医疗对话数据处理器
    处理多轮对话数据，转换为训练格式
    """
    
    def __init__(self, tokenizer, max_length=2048):
        self.tokenizer = tokenizer
        self.max_length = max_length
        
        # 系统提示词 - 训练时使用的简化版本
        self.system_prompt = """你是小文，专业的宠物医生AI助手。

核心要求：
- 温暖专业的语调
- 严格单问制：每次只问一个问题
- 不重复已问过的问题
- 安全第一，紧急情况立即建议就医

请提供专业的宠物医疗咨询服务。"""

    def format_conversation(self, conversation: List[Dict]) -> str:
        """
        将对话格式化为ChatML格式
        
        Args:
            conversation: 对话列表，每个元素包含role和content
            
        Returns:
            格式化后的对话字符串
        """
        formatted = f"<|im_start|>system\n{self.system_prompt}<|im_end|>\n"
        
        for turn in conversation:
            role = turn["role"]
            content = turn["content"]
            formatted += f"<|im_start|>{role}\n{content}<|im_end|>\n"
            
        return formatted

    def process_dataset(self, data_path: str) -> Dataset:
        """
        处理数据集文件
        
        Args:
            data_path: 数据文件路径
            
        Returns:
            处理后的Dataset对象
        """
        # 加载原始数据
        with open(data_path, 'r', encoding='utf-8') as f:
            raw_data = json.load(f)
        
        processed_data = []
        
        for conversation in raw_data:
            # 将每个对话转换为训练样本
            messages = conversation.get("messages", [])
            if len(messages) < 2:  # 至少需要一轮对话
                continue
                
            # 格式化对话
            formatted_text = self.format_conversation(messages)
            
            # 分词并截断
            tokens = self.tokenizer(
                formatted_text,
                max_length=self.max_length,
                truncation=True,
                padding=False,
                return_tensors=None
            )
            
            processed_data.append({
                "input_ids": tokens["input_ids"],
                "attention_mask": tokens["attention_mask"],
                "labels": tokens["input_ids"].copy()  # 因果语言模型的标签就是input_ids
            })
        
        return Dataset.from_list(processed_data)

# ============================================================================
# 自定义训练器
# ============================================================================

class PetMedicalTrainer(Trainer):
    """
    自定义训练器，添加特定的评估指标
    """
    
    def compute_loss(self, model, inputs, return_outputs=False):
        """
        计算损失，添加自定义正则化
        """
        labels = inputs.get("labels")
        outputs = model(**inputs)
        
        # 基础语言模型损失
        loss = outputs.loss
        
        # 可以在这里添加自定义损失项
        # 例如：鼓励生成单个问题的损失项
        
        return (loss, outputs) if return_outputs else loss

# ============================================================================
# 主训练函数
# ============================================================================

def main():
    parser = argparse.ArgumentParser(description="Qwen3-14B QLoRA微调")
    parser.add_argument("--data_path", type=str, required=True, help="训练数据路径")
    parser.add_argument("--output_dir", type=str, default="./output", help="输出目录")
    parser.add_argument("--model_name", type=str, default="Qwen/Qwen2.5-14B-Instruct", help="基础模型名称")
    parser.add_argument("--wandb_project", type=str, default="qwen-pet-medical", help="WandB项目名称")
    parser.add_argument("--resume_from_checkpoint", type=str, default=None, help="从检查点恢复")
    
    args = parser.parse_args()
    
    # 初始化WandB
    wandb.init(
        project=args.wandb_project,
        name=f"qwen-14b-qlora-{wandb.util.generate_id()}",
        config={
            "model": args.model_name,
            "method": "QLoRA",
            "dataset": "pet_medical_conversations"
        }
    )
    
    # 加载配置
    config = MediumDatasetConfig()
    
    # 设置量化配置
    bnb_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_compute_dtype=torch.float16,
        bnb_4bit_use_double_quant=True,
    )
    
    # 加载分词器
    print("加载分词器...")
    tokenizer = AutoTokenizer.from_pretrained(
        args.model_name,
        trust_remote_code=True,
        padding_side="right"  # 对于训练，使用右填充
    )
    
    # 设置特殊token
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # 加载模型
    print("加载模型...")
    model = AutoModelForCausalLM.from_pretrained(
        args.model_name,
        quantization_config=bnb_config,
        device_map="auto",
        trust_remote_code=True,
        torch_dtype=torch.float16
    )
    
    # 应用LoRA
    print("应用LoRA配置...")
    model = get_peft_model(model, config.lora_config)
    model.print_trainable_parameters()
    
    # 处理数据
    print("处理训练数据...")
    data_processor = PetMedicalDataProcessor(tokenizer, config.max_seq_length)
    dataset = data_processor.process_dataset(args.data_path)
    
    # 划分训练集和验证集
    train_test_split = dataset.train_test_split(test_size=0.1, seed=42)
    train_dataset = train_test_split["train"]
    eval_dataset = train_test_split["test"]
    
    print(f"训练样本数: {len(train_dataset)}")
    print(f"验证样本数: {len(eval_dataset)}")
    
    # 估算训练成本
    cost_info = estimate_training_cost(config, len(train_dataset))
    wandb.log(cost_info)
    
    # 更新输出目录
    config.training_args.output_dir = args.output_dir
    
    # 创建训练器
    trainer = PetMedicalTrainer(
        model=model,
        args=config.training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        tokenizer=tokenizer,
    )
    
    # 开始训练
    print("开始训练...")
    if args.resume_from_checkpoint:
        trainer.train(resume_from_checkpoint=args.resume_from_checkpoint)
    else:
        trainer.train()
    
    # 保存最终模型
    print("保存模型...")
    trainer.save_model()
    tokenizer.save_pretrained(args.output_dir)
    
    # 保存训练日志
    trainer.save_state()
    
    print("训练完成！")
    wandb.finish()

# ============================================================================
# 数据格式示例
# ============================================================================

def create_sample_data():
    """
    创建示例数据格式，供参考
    """
    sample_data = [
        {
            "conversation_id": "conv_001",
            "messages": [
                {
                    "role": "user",
                    "content": "我的猫发烧了"
                },
                {
                    "role": "assistant", 
                    "content": "我理解您的担心，猫咪发烧确实需要关注。请问您是如何发现猫咪发烧的？是摸起来感觉很热，还是用体温计测量过了？"
                },
                {
                    "role": "user",
                    "content": "用体温计测的，39.8度"
                },
                {
                    "role": "assistant",
                    "content": "39.8度确实是发烧了。请问这种发烧症状持续多长时间了？"
                }
            ],
            "metadata": {
                "pet_type": "cat",
                "symptoms": ["fever"],
                "conversation_length": 2
            }
        }
    ]
    
    # 保存示例数据
    with open("sample_data.json", "w", encoding="utf-8") as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)
    
    print("示例数据已保存到 sample_data.json")

if __name__ == "__main__":
    main()
