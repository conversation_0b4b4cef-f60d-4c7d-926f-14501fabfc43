# Qwen3-14B QLoRA微调指南

基于200条宠物医疗对话数据集的Qwen3-14B模型QLoRA微调完整方案。

## 项目概述

本项目针对宠物医疗咨询场景，使用QLoRA技术对Qwen3-14B模型进行微调，目标是训练一个能够：
- 严格遵循"单问制"规则
- 避免重复询问
- 提供专业医疗建议
- 保持对话连贯性

的专业宠物医疗AI助手。

## 文件结构

```
xiaowen-chat-prompt/
├── training_config.py      # 训练配置文件
├── train_qwen_qlora.py    # 主训练脚本
├── evaluate_model.py      # 模型评估脚本
├── training_analysis.md   # 详细的超参数分析
├── README.md             # 使用指南（本文件）
└── data/                 # 数据目录
    ├── train_data.json   # 训练数据
    └── test_data.json    # 测试数据
```

## 环境准备

### 1. 硬件要求

**最低配置：**
- GPU: RTX 4090 (24GB) 或 V100 (32GB)
- 内存: 32GB DDR4
- 存储: 100GB SSD

**推荐配置：**
- GPU: A100 (40GB) 或 RTX 4090 (24GB)
- 内存: 64GB DDR4
- 存储: 200GB NVMe SSD

### 2. 软件环境

```bash
# 创建虚拟环境
conda create -n qwen-qlora python=3.10
conda activate qwen-qlora

# 安装依赖
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install transformers==4.36.0
pip install peft==0.7.0
pip install datasets==2.14.0
pip install bitsandbytes==0.41.3
pip install accelerate==0.24.0
pip install wandb
pip install scipy
```

## 数据准备

### 1. 数据格式

训练数据应为JSON格式，每个对话包含多轮交互：

```json
[
  {
    "conversation_id": "conv_001",
    "messages": [
      {
        "role": "user",
        "content": "我的猫发烧了"
      },
      {
        "role": "assistant",
        "content": "我理解您的担心，猫咪发烧确实需要关注。请问您是如何发现猫咪发烧的？是摸起来感觉很热，还是用体温计测量过了？"
      },
      {
        "role": "user", 
        "content": "用体温计测的，39.8度"
      },
      {
        "role": "assistant",
        "content": "39.8度确实是发烧了。请问这种发烧症状持续多长时间了？"
      }
    ],
    "metadata": {
      "pet_type": "cat",
      "symptoms": ["fever"],
      "conversation_length": 2
    }
  }
]
```

### 2. 数据质量要求

- **对话完整性**: 每个对话至少包含2轮交互
- **规则遵循**: 助手回复必须遵循单问制
- **专业性**: 医疗建议需要准确专业
- **多样性**: 覆盖不同宠物类型和症状

## 训练流程

### 1. 配置检查

```python
# 检查推荐配置
python training_config.py
```

输出示例：
```
检测到中等数据集，使用标准配置
推荐的训练配置:
LoRA rank: 32
学习率: 0.0002
训练轮次: 5
批次大小: 1
梯度累积: 16

训练成本估算:
总训练步数: 625
预计训练时间: 6.1 小时
预计GPU成本: $21.35 USD
推荐GPU: RTX 4090 (24GB) 或 A100 (40GB)
```

### 2. 开始训练

```bash
# 基础训练命令
python train_qwen_qlora.py \
    --data_path ./data/train_data.json \
    --output_dir ./output \
    --wandb_project qwen-pet-medical

# 从检查点恢复训练
python train_qwen_qlora.py \
    --data_path ./data/train_data.json \
    --output_dir ./output \
    --resume_from_checkpoint ./output/checkpoint-500
```

### 3. 训练监控

训练过程中关注以下指标：
- **训练损失**: 应从~2.5降到~1.5以下
- **验证损失**: 应跟随训练损失下降
- **困惑度**: 应从~12降到~4以下
- **GPU利用率**: 应保持在80%以上

## 模型评估

### 1. 运行评估

```bash
python evaluate_model.py \
    --model_path ./output \
    --test_data ./data/test_data.json
```

### 2. 评估指标

- **单问制遵循率**: 目标 >85%
- **重复询问率**: 目标 <10%
- **医疗准确性**: 目标 >85%
- **对话连贯性**: 目标 >80%

### 3. 评估报告示例

```
==================================================
模型评估报告
==================================================
单问制遵循率: 87.50%
重复询问率: 8.33%
医疗准确性: 82.15%
对话连贯性: 85.20%
==================================================
综合评分: 84.32%
评估等级: 良好
==================================================
```

## 超参数调优建议

### 1. 常见问题与解决方案

**过拟合问题：**
```python
# 减少LoRA rank
lora_config.r = 16
lora_config.lora_alpha = 32

# 增加dropout
lora_config.lora_dropout = 0.15

# 减少训练轮次
training_args.num_train_epochs = 3
```

**欠拟合问题：**
```python
# 增加LoRA rank
lora_config.r = 64
lora_config.lora_alpha = 128

# 提高学习率
training_args.learning_rate = 3e-4

# 增加训练轮次
training_args.num_train_epochs = 8
```

**训练不稳定：**
```python
# 降低学习率
training_args.learning_rate = 1e-4

# 增加预热步数
training_args.warmup_ratio = 0.15

# 启用梯度裁剪
training_args.max_grad_norm = 1.0
```

### 2. 超参数搜索

如果有充足的计算资源，可以进行网格搜索：

```python
search_space = {
    "learning_rate": [1e-4, 2e-4, 3e-4],
    "lora_r": [16, 32, 64],
    "lora_dropout": [0.05, 0.1, 0.15],
    "num_epochs": [3, 5, 8]
}
```

## 部署使用

### 1. 模型推理

```python
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel

# 加载模型
tokenizer = AutoTokenizer.from_pretrained("./output")
base_model = AutoModelForCausalLM.from_pretrained("Qwen/Qwen2.5-14B-Instruct")
model = PeftModel.from_pretrained(base_model, "./output")

# 生成回复
def generate_response(conversation_history):
    # 格式化输入
    formatted_input = format_conversation(conversation_history)
    
    # 生成回复
    inputs = tokenizer(formatted_input, return_tensors="pt")
    outputs = model.generate(**inputs, max_new_tokens=256)
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    return response
```

### 2. 性能优化

- **量化推理**: 使用4bit量化减少内存占用
- **批量推理**: 支持多个对话并行处理
- **缓存优化**: 缓存常用的对话模板

## 预期效果

根据我们的分析，使用推荐的超参数配置，您可以期待：

| 指标 | 训练前 | 训练后目标 |
|------|--------|------------|
| 单问制遵循率 | 60% | 85%+ |
| 重复询问率 | 40% | <10% |
| 医疗准确性 | 70% | 85%+ |
| 对话连贯性 | 70% | 80%+ |
| 综合评分 | 65% | 85%+ |

## 常见问题

### Q1: 训练过程中出现OOM错误怎么办？
A1: 
- 减少batch_size到1
- 增加gradient_accumulation_steps
- 启用gradient_checkpointing
- 使用更小的max_seq_length

### Q2: 模型不遵循单问制怎么办？
A2:
- 检查训练数据质量
- 增加单问制相关的训练样本
- 调整损失函数，增加规则遵循的权重
- 延长训练时间

### Q3: 如何提高医疗准确性？
A3:
- 增加高质量的医疗对话数据
- 使用专业兽医标注的数据
- 引入医疗知识图谱
- 考虑使用强化学习进一步优化

## 技术支持

如果在使用过程中遇到问题，请：
1. 检查硬件配置是否满足要求
2. 确认数据格式是否正确
3. 查看训练日志中的错误信息
4. 参考training_analysis.md中的详细分析

---

**注意**: 本方案专门针对200条对话、每个对话约10轮的数据集优化。如果您的数据规模显著不同，请相应调整超参数配置。
